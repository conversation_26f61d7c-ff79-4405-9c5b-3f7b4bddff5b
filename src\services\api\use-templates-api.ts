import axiosInstance from 'src/utils/axios';
import { useApiServices } from 'src/services/hooks/use-api-services';

// Define the API endpoints for templates
export const templateEndpoints = {
  list: '/templates',
  details: '/templates',
};

// Define the Category interface
export interface Category {
  name: string;
  description: string;
  icon: string;
  theme: string;
}

// Define the Template data type based on the new API response
export interface Template {
  id: number;
  name: string;
  description: string;
  type: 'SINGLE' | 'TEAM';
  model: 'GPT_4O_MINI' | 'GPT_4O' | 'CLAUDE_3_5_SONNET';
  category: Category;
}

// Define the API response structure
export interface TemplatesResponse {
  templates: Template[];
}

// Create a hook to use the templates API
export const useTemplatesApi = () => {
  const apiServices = useApiServices({ axiosInstance });

  // Get all templates
  const useGetTemplates = () => {
    return apiServices.useGetListService<TemplatesResponse>({
      url: templateEndpoints.list,
    });
  };

  // Get a single template by ID
  const useGetTemplate = (id: string) => {
    return apiServices.useGetItemService<Template>({
      url: templateEndpoints.details,
      id,
    });
  };

  // Create a new template
  const useCreateTemplate = (onSuccess?: (data: any) => void) => {
    return apiServices.usePostService<Template, any>({
      url: templateEndpoints.list,
      onSuccess,
    });
  };

  // Update a template
  const useUpdateTemplate = (id: string, onSuccess?: () => void) => {
    return apiServices.usePutService<Template>({
      url: templateEndpoints.details,
      id,
      onSuccess,
    });
  };

  // Delete a template
  const useDeleteTemplate = (onSuccess?: () => void) => {
    return apiServices.useDeleteService<any>({
      url: templateEndpoints.details,
      onSuccess,
    });
  };

  return {
    useGetTemplates,
    useGetTemplate,
    useCreateTemplate,
    useUpdateTemplate,
    useDeleteTemplate,
  };
};
