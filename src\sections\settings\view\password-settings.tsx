import { useState } from 'react';
import { <PERSON>, Stack, Typo<PERSON>, TextField, InputAdornment, IconButton, Alert } from '@mui/material';
import { useTranslation } from 'react-i18next';

import { Iconify } from 'src/components/iconify';
import { AppButton } from 'src/components/common';
import { useProfileView } from './use-profile-view';

// ----------------------------------------------------------------------

export function PasswordSettings() {
  const { t } = useTranslation();
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const {
    passwordForm,
    isChangingPassword,
    passwordError,
    isPasswordFormValid,
    handlePasswordFormChange,
    handleChangePassword,
  } = useProfileView();

  return (
    <Stack spacing={3}>
      <Typography variant="h3">{t('Password')}</Typography>

      {/* Show password change error if any */}
      {passwordError && (
        <Alert severity="error">
          Error changing password: {passwordError.message}
        </Alert>
      )}

      <Stack spacing={2}>
        {/* Current Password Field */}
        <Box sx={{ mb: 1 }}>
          <Typography variant="body2" component="div" sx={{ mb: 1 }}>
            {t('Current Password')}
          </Typography>
          <TextField
            fullWidth
            size="small"
            type={showCurrentPassword ? 'text' : 'password'}
            value={passwordForm.currentPassword}
            onChange={(e) => handlePasswordFormChange('currentPassword', e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <IconButton edge="start">
                    <Iconify icon="solar:key-outline" width={24} />
                  </IconButton>
                </InputAdornment>
              ),
              endAdornment: (
                <InputAdornment position="end">
                  <IconButton
                    edge="end"
                    onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                  >
                    <Iconify
                      icon={showCurrentPassword ? 'mdi:eye-off-outline' : 'mdi:eye-outline'}
                      width={24}
                      sx={{ color: 'primary.main' }}
                    />
                  </IconButton>
                </InputAdornment>
              ),
            }}
          />
        </Box>

        {/* New Password Field */}
        <Box sx={{ mb: 1 }}>
          <Typography variant="body2" component="div" sx={{ mb: 1 }}>
            {t('New Password')}
          </Typography>
          <TextField
            fullWidth
            size="small"
            helperText="Minimum 8 characters"
            type={showNewPassword ? 'text' : 'password'}
            value={passwordForm.newPassword}
            onChange={(e) => handlePasswordFormChange('newPassword', e.target.value)}
            error={passwordForm.newPassword.length > 0 && passwordForm.newPassword.length < 8}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <IconButton edge="start">
                    <Iconify icon="solar:key-outline" width={24} />
                  </IconButton>
                </InputAdornment>
              ),
              endAdornment: (
                <InputAdornment position="end">
                  <IconButton edge="end" onClick={() => setShowNewPassword(!showNewPassword)}>
                    <Iconify
                      icon={showNewPassword ? 'mdi:eye-off-outline' : 'mdi:eye-outline'}
                      width={24}
                      sx={{ color: 'primary.main' }}
                    />
                  </IconButton>
                </InputAdornment>
              ),
            }}
          />
        </Box>

        {/* Confirm New Password Field */}
        <Box sx={{ mb: 1 }}>
          <Typography variant="body2" component="div" sx={{ mb: 1 }}>
            {t('Confirm New Password')}
          </Typography>
          <TextField
            fullWidth
            size="small"
            type={showConfirmPassword ? 'text' : 'password'}
            value={passwordForm.confirmPassword}
            onChange={(e) => handlePasswordFormChange('confirmPassword', e.target.value)}
            error={passwordForm.confirmPassword.length > 0 && passwordForm.newPassword !== passwordForm.confirmPassword}
            helperText={passwordForm.confirmPassword.length > 0 && passwordForm.newPassword !== passwordForm.confirmPassword ? 'Passwords do not match' : ''}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <IconButton edge="start">
                    <Iconify icon="solar:key-outline" width={24} />
                  </IconButton>
                </InputAdornment>
              ),
              endAdornment: (
                <InputAdornment position="end">
                  <IconButton
                    edge="end"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  >
                    <Iconify
                      icon={showConfirmPassword ? 'mdi:eye-off-outline' : 'mdi:eye-outline'}
                      width={24}
                      sx={{ color: 'primary.main' }}
                    />
                  </IconButton>
                </InputAdornment>
              ),
            }}
          />
        </Box>
      </Stack>

      <Stack direction="row" justifyContent="end" sx={{ pt: 3 }}>
         <AppButton
          variant="contained"
          onClick={handleChangePassword}
          disabled={!isPasswordFormValid || isChangingPassword}
          sx={{
            minWidth: '140px',
            whiteSpace:'nowrap',
            borderRadius: 1,
            alignSelf: 'flex-end',
            mt: 3,
            bgcolor: 'primary.main',
          }}
          label={isChangingPassword ? 'Changing...' : t('Change Password')}
        />
      </Stack>
    </Stack>
  );
}
