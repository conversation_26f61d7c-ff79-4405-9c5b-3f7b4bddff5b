import { forwardRef } from 'react';
import { Box, Typography, Stack } from '@mui/material';
import { useTheme } from '@mui/material/styles';
import { RouterLink } from 'src/routes/components';

// ----------------------------------------------------------------------

interface WorkforcesLogoProps {
  sx?: any;
  disableLink?: boolean;
  href?: string;
}

export const WorkforcesLogo = forwardRef<HTMLDivElement, WorkforcesLogoProps>(
  ({ disableLink = false, href = '/', sx, ...other }, ref) => {
    const theme = useTheme();
    const PRIMARY_MAIN = theme.vars.palette.primary.main;
    const PRIMARY_LIGHT = theme.vars.palette.primary.light;

    const logo = (
      <Stack
        direction="row"
        alignItems="center"
        spacing={{ xs: 0.5, sm: 0.75, md: 1 }}
        sx={{
          transition: theme.transitions.create(['gap'], {
            duration: theme.transitions.duration.shorter,
          }),
        }}
      >
        <Box
          ref={ref}
          sx={{
            width: { xs: 20, sm: 22, md: 24 },
            height: { xs: 20, sm: 22, md: 24 },
            display: 'inline-flex',
            ...sx,
          }}
          {...other}
        >
          <svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <circle cx="12" cy="12" r="12" fill={PRIMARY_LIGHT} fillOpacity="0.16" />
            <path
              d="M17.5 12C17.5 15.0376 15.0376 17.5 12 17.5C8.96243 17.5 6.5 15.0376 6.5 12C6.5 8.96243 8.96243 6.5 12 6.5C15.0376 6.5 17.5 8.96243 17.5 12Z"
              stroke={PRIMARY_MAIN}
              strokeWidth="1.5"
            />
            <path
              d="M14.5 9.5L9.5 14.5M9.5 9.5L14.5 14.5"
              stroke={PRIMARY_MAIN}
              strokeWidth="1.5"
              strokeLinecap="round"
            />
          </svg>
        </Box>
        <Typography
          variant="subtitle1"
          sx={{
            color: 'text.primary',
            fontWeight: 600,
            fontSize: { xs: '0.875rem', sm: '0.9375rem', md: '1rem' },
            display: { xs: 'none', sm: 'inline-flex' },
          }}
        >
          Workforces
        </Typography>
      </Stack>
    );

    if (disableLink) {
      return logo;
    }

    return (
      <Box
        component={RouterLink}
        href={href}
        sx={{ display: 'inline-flex', textDecoration: 'none' }}
      >
        {logo}
      </Box>
    );
  }
);
