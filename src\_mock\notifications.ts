import { sub } from 'date-fns';
import { NotificationItemProps } from './_notifications';

// ----------------------------------------------------------------------

const now = new Date();

// Create a time at 9:32 AM today
const createTimeAt932AM = () => {
  const date = new Date();
  date.setHours(9, 32, 0, 0);
  return date;
};

// Create a time at 1 day ago
const createTimeAt1DayAgo = () => {
  const date = new Date();
  date.setDate(date.getDate() - 1);
  return date;
};

// Create a time at 3 days ago
const createTimeAt3DaysAgo = () => {
  const date = new Date();
  date.setDate(date.getDate() - 3);
  return date;
};

export const _notifications: NotificationItemProps[] = [
  // Today notifications
  {
    id: '1',
    title: 'Question answered!',
    description: 'Your question #123456 has been answered, check it now.',
    type: 'question',
    isUnRead: true,
    createdAt: createTimeAt932AM(),
    time: '9:32 AM',
    avatar: null,
  },
  {
    id: '2',
    title: 'New team member joined',
    description: 'Sarah Williams has joined your AI Team project.',
    type: 'team',
    isUnRead: true,
    createdAt: sub(now, { hours: 2 }),
    time: '2h ago',
    avatar: '/assets/images/avatar/avatar_2.jpg',
  },
  {
    id: '3',
    title: 'Project update',
    description: 'Your project "UX Research" has been updated with new assets.',
    type: 'project',
    isUnRead: true,
    createdAt: sub(now, { hours: 4 }),
    time: '4h ago',
    avatar: null,
  },
  {
    id: '4',
    title: 'New message',
    description: 'You have received a new message from David Johnson.',
    type: 'message',
    isUnRead: false,
    createdAt: sub(now, { hours: 5 }),
    time: '5h ago',
    avatar: '/assets/images/avatar/avatar_3.jpg',
  },
  {
    id: '5',
    title: 'Task completed',
    description: 'Your task "Create wireframes" has been marked as completed.',
    type: 'task',
    isUnRead: false,
    createdAt: sub(now, { hours: 8 }),
    time: '8h ago',
    avatar: null,
  },

  // Yesterday notifications
  {
    id: '6',
    title: 'Question answered!',
    description: 'Your question #123456 has been answered, you can view the response.',
    type: 'question',
    isUnRead: false,
    createdAt: createTimeAt1DayAgo(),
    time: '1d ago',
    avatar: null,
  },
  {
    id: '7',
    title: 'Credits added',
    description: 'Your account has been credited with 500 additional credits.',
    type: 'account',
    isUnRead: false,
    createdAt: createTimeAt1DayAgo(),
    time: '1d ago',
    avatar: null,
  },
  {
    id: '8',
    title: 'Meeting scheduled',
    description: 'Team meeting has been scheduled for tomorrow at 10:00 AM.',
    type: 'calendar',
    isUnRead: false,
    createdAt: createTimeAt1DayAgo(),
    time: '1d ago',
    avatar: null,
  },

  // Earlier notifications
  {
    id: '9',
    title: 'Account security',
    description: 'We noticed a login from a new device. Please verify it was you.',
    type: 'security',
    isUnRead: false,
    createdAt: createTimeAt3DaysAgo(),
    time: '3d ago',
    avatar: null,
  },
  {
    id: '10',
    title: 'Subscription renewal',
    description: 'Your subscription will renew in 7 days. Review your plan now.',
    type: 'billing',
    isUnRead: false,
    createdAt: sub(now, { days: 5 }),
    time: '5d ago',
    avatar: null,
  },
  {
    id: '11',
    title: 'New feature available',
    description: 'Try our new AI-powered design suggestions feature.',
    type: 'feature',
    isUnRead: false,
    createdAt: sub(now, { days: 7 }),
    time: '7d ago',
    avatar: null,
  },
];
