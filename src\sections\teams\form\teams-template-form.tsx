import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON>per,
  Step,
  StepLabel,
  TextField,
  Radio,
  RadioGroup,
  FormControlLabel,
  FormControl,
  Card,
  Chip,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Avatar,
  IconButton,
  InputAdornment,
  Stack,
  Checkbox,
  Grid,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import { Icon } from '@iconify/react';
import { useRouter } from 'src/routes/hooks';
import { AppButton, AppContainer } from 'src/components/common';
import { Iconify } from 'src/components/iconify';
import { Field, Form } from 'src/components/hook-form';
import { paths } from 'src/routes/paths';
import useTeamTemplateForm from './use-team-template-form';

// Custom styled components to match the design - outlined circles with no connecting lines
const CustomStepIcon = styled('div')<{ active?: boolean; completed?: boolean }>(
  ({ theme, active, completed }) => ({
    width: 32,
    height: 32,
    borderRadius: '50%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    fontSize: '0.875rem',
    fontWeight: 600,
    backgroundColor: active
      ? theme.palette.primary.main
      : completed
        ? theme.palette.success.main
        : theme.palette.background.paper,
    color: active
      ? theme.palette.primary.contrastText
      : completed
        ? theme.palette.success.contrastText
        : theme.palette.text.secondary,
    border: active
      ? 'none'
      : completed
        ? 'none'
        : `2px solid ${theme.palette.divider}`,
    transition: 'all 0.2s ease-in-out',
    boxShadow: theme.shadows[1],
  })
);

// Custom styled chip-like step container
const StepChipContainer = styled(Box)<{ active?: boolean; completed?: boolean }>(
  ({ theme, active }) => ({
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    padding: '12px 16px',
    borderRadius: '24px',
    backgroundColor: active
      ? theme.palette.primary.main + '20'
      : 'transparent',
    border: active
      ? `1px solid ${theme.palette.primary.main}50`
      : '1px solid transparent',
    transition: 'all 0.2s ease-in-out',
    minWidth: '120px',
  })
);

const TeamsTemplateForm = () => {
  const router = useRouter();
  const [activeStep, setActiveStep] = useState(0);
  const [teamName, setTeamName] = useState('');
  const [description, setDescription] = useState('');
  const [flowControl, setFlowControl] = useState('auto');
  const [membersExpanded, setMembersExpanded] = useState(true);
  const [toolsExpanded, setToolsExpanded] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const { methods, FREQUENCY_OPTIONS } = useTeamTemplateForm();
  // Configuration state
  const [configExpanded, setConfigExpanded] = useState({
    gmail: false,
    facebook: false,
    pdf: false,
    x: true, // X (Twitter) is expanded in the image
  });
  const [apiKeys, setApiKeys] = useState({
    gmail: { value: '', status: 'linked' }, // linked, failed, or null
    facebook: { value: '', status: 'linked' },
    pdf: { value: '', status: 'linked' },
    x: { value: '', status: 'failed' },
  });

  // Sample members data
  const [members] = useState([
    { id: 1, name: 'Layla Al-Farsi', avatar: '/assets/images/avatar/avatar-1.jpg', added: false },
    { id: 2, name: 'Omar Al-Hakim', avatar: '/assets/images/avatar/avatar-2.jpg', added: true },
    { id: 3, name: 'Zayd Al-Mansoori', avatar: '/assets/images/avatar/avatar-3.jpg', added: true },
  ]);

  // Configuration tools data
  const configTools = [
    {
      id: 'gmail',
      name: 'Get emails from Gmail',
      icon: 'logos:google-gmail',
      color: '#EA4335',
      status: apiKeys.gmail.status,
    },
    {
      id: 'facebook',
      name: 'Get the posts of a Facebook page',
      icon: 'logos:facebook',
      color: '#1877F2',
      status: apiKeys.facebook.status,
    },
    {
      id: 'pdf',
      name: 'Summarize a PDF file',
      icon: 'vscode-icons:file-type-pdf2',
      color: '#DC2626',
      status: apiKeys.pdf.status,
    },
    {
      id: 'x',
      name: 'Get the posts of a X account',
      icon: 'ri:twitter-x-fill',
      color: '#000000',
      status: apiKeys.x.status,
    },
  ];

  const steps = [
    'Team Information',
    'Tools & Members',
    'Configuration',
    'Instructions',
    'Frequency',
  ];

  const handleNext = () => {
    setActiveStep((prevActiveStep) => prevActiveStep + 1);
  };

  const handleCancel = () => {
    router.push(paths.dashboard.teams.chat);
  };

  // Configuration handlers
  const handleConfigToggle = (toolId: string) => {
    setConfigExpanded((prev) => ({
      ...prev,
      [toolId]: !prev[toolId as keyof typeof prev],
    }));
  };

  const handleApiKeyChange = (toolId: string, value: string) => {
    setApiKeys((prev) => ({
      ...prev,
      [toolId]: { ...prev[toolId as keyof typeof prev], value },
    }));
  };

  const handleSaveApiKey = (toolId: string) => {
    const apiKey = apiKeys[toolId as keyof typeof apiKeys].value;
    if (apiKey.trim()) {
      // Simulate API call - randomly set success or failure for demo
      const isSuccess = Math.random() > 0.3;
      setApiKeys((prev) => ({
        ...prev,
        [toolId]: {
          ...prev[toolId as keyof typeof prev],
          status: isSuccess ? 'linked' : 'failed',
        },
      }));
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'linked':
        return '#10B981';
      case 'failed':
        return '#EF4444';
      default:
        return '#6B7280';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'linked':
        return 'Linked';
      case 'failed':
        return 'Failed';
      default:
        return '';
    }
  };

  const renderStepContent = (step: number) => {
    switch (step) {
      case 0:
        return (
          <Box>
            <Typography variant="h5" fontWeight={700} mb={1} color="text.primary">
              Team Information
            </Typography>
            <Typography variant="body2" color="text.secondary" mb={1} fontSize="0.875rem">
              Create your own team and view your team members and tools
            </Typography>
            <Card
              sx={{
                background: 'background.paper',
                padding: '20px',
                display: 'flex',
                flexDirection: 'column',
                gap: '16px',
                border: '1px solid',
                borderColor: 'divider',
              }}
            >
              <Box>
                <Typography variant="body2" fontWeight={500} color="text.primary">
                  Team Name
                </Typography>
                <Field.Text name="name" sx={{color:'transparent'}} fullWidth placeholder="Type your team name" />
              </Box>

              <Box>
                <Typography variant="body2" fontWeight={500} color="text.primary">
                  Description
                </Typography>
                <Field.Text
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      height: '100px',
                    },
                  }}
                  name="description"
                  fullWidth
                  multiline
                  rows={3}
                  placeholder="Type your team description"
                />
              </Box>

              <Box>
                <Typography variant="body2" fontWeight={500} color="text.primary">
                  Flow Control
                </Typography>
                <FormControl>
                  <RadioGroup
                    row
                    value={flowControl}
                    onChange={(e) => setFlowControl(e.target.value)}
                    sx={{ gap: 4 }}
                  >
                    <FormControlLabel
                      value="auto"
                      control={
                        <Radio
                          sx={{
                            color: 'text.secondary',
                            '&.Mui-checked': {
                              color: 'primary.main',
                            },
                            '& .MuiSvgIcon-root': {
                              fontSize: 20,
                            },
                          }}
                        />
                      }
                      label={
                        <Typography
                          variant="body2"
                          sx={{ fontSize: '0.875rem', color: 'text.primary', fontWeight: 500 }}
                        >
                          Auto
                        </Typography>
                      }
                    />
                    <FormControlLabel
                      value="manual"
                      control={
                        <Radio
                          sx={{
                            color: 'text.secondary',
                            '&.Mui-checked': {
                              color: 'primary.main',
                            },
                            '& .MuiSvgIcon-root': {
                              fontSize: 20,
                            },
                          }}
                        />
                      }
                      label={
                        <Typography
                          variant="body2"
                          sx={{ fontSize: '0.875rem', color: 'text.primary', fontWeight: 500 }}
                        >
                          Manual
                        </Typography>
                      }
                    />
                  </RadioGroup>
                </FormControl>
              </Box>
            </Card>
          </Box>
        );
      case 1:
        return (
          <Box>
            <Typography variant="h5" fontWeight={700} mb={1} color="text.primary">
              Tools & Members
            </Typography>
            <Typography variant="body2" color="text.secondary" mb={4} fontSize="0.875rem">
              Add team members and tools
            </Typography>

            {/* Members Accordion */}
            <Accordion
              expanded={membersExpanded}
              onChange={() => setMembersExpanded(!membersExpanded)}
              sx={{
                mb: 3,
                boxShadow: 'none',
                border: '1px solid',
                borderColor: 'divider',
                borderRadius: '8px !important',
                bgcolor: 'background.paper',
                '&:before': {
                  display: 'none',
                },
              }}
            >
              <AccordionSummary
                expandIcon={<Icon icon="eva:chevron-down-fill" />}
                sx={{
                  backgroundColor: 'action.hover',
                  borderRadius: '8px',
                  '& .MuiAccordionSummary-content': {
                    margin: '12px 0',
                  },
                }}
              >
                <Typography variant="h6" fontWeight={600} color="text.primary">
                  Members
                </Typography>
              </AccordionSummary>
              <AccordionDetails sx={{ p: 3 }}>
                {/* Search Field */}
                <Card
                  sx={{
                    background: (theme) =>
                      theme.palette.mode === 'dark'
                        ? theme.palette.grey[800]
                        : theme.palette.background.neutral,
                    padding: '20px',
                    display: 'flex',
                    flexDirection: 'column',
                    gap: '16px',
                    border: (theme) =>
                      theme.palette.mode === 'dark'
                        ? `1px solid ${theme.palette.grey[700]}`
                        : 'none',
                  }}
                >
                  <TextField
                    fullWidth
                    placeholder="Search"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <Icon icon="eva:search-fill" />
                        </InputAdornment>
                      ),
                    }}
                    sx={{
                      mb: 3,
                      '& .MuiOutlinedInput-root': {
                        bgcolor: (theme) =>
                          theme.palette.mode === 'dark'
                            ? theme.palette.grey[700]
                            : '#F9FAFB',
                        borderRadius: '8px',
                        color: 'text.primary',
                        '& fieldset': {
                          borderColor: (theme) =>
                            theme.palette.mode === 'dark'
                              ? theme.palette.grey[600]
                              : '#E5E7EB',
                        },
                        '&:hover fieldset': {
                          borderColor: (theme) =>
                            theme.palette.mode === 'dark'
                              ? theme.palette.grey[500]
                              : '#D1D5DB',
                        },
                        '&.Mui-focused fieldset': {
                          borderColor: 'primary.main',
                        },
                      },
                      '& .MuiInputBase-input': {
                        color: 'text.primary',
                        '&::placeholder': {
                          color: 'text.secondary',
                          opacity: 1,
                        },
                      },
                    }}
                  />

                  {/* Members List */}
                  <Box>
                    {members.map((member) => (
                      <Box
                        key={member.id}
                        display="flex"
                        alignItems="center"
                        justifyContent="space-between"
                        py={2}
                        borderBottom={(theme) =>
                          theme.palette.mode === 'dark'
                            ? `1px solid ${theme.palette.grey[700]}`
                            : '1px solid #F3F4F6'
                        }
                        sx={{
                          background: (theme) =>
                            theme.palette.mode === 'dark'
                              ? theme.palette.grey[700]
                              : '#F1EFF3',
                          mt: '8px',
                          borderRadius: '16px',
                          padding: '8px',
                        }}
                      >
                        <Box display="flex" alignItems="center" gap={2}>
                          <Avatar src={member.avatar} sx={{ width: 40, height: 40 }} />
                          <Typography variant="body2" fontWeight={500} color="text.primary">
                            {member.name}
                          </Typography>
                        </Box>
                        <IconButton
                          size="small"
                          sx={{
                            width: 32,
                            height: 32,
                            borderRadius: '8px',
                            border: '1px solid',
                            borderColor: member.added
                              ? (theme) =>
                                  theme.palette.mode === 'dark'
                                    ? theme.palette.error.main
                                    : '#F87171'
                              : 'primary.main',
                            backgroundColor: member.added
                              ? (theme) =>
                                  theme.palette.mode === 'dark'
                                    ? 'rgba(244, 67, 54, 0.1)'
                                    : '#FEF2F2'
                              : (theme) =>
                                  theme.palette.mode === 'dark'
                                    ? 'rgba(139, 92, 246, 0.1)'
                                    : '#F3E8FF',
                            color: member.added
                              ? (theme) =>
                                  theme.palette.mode === 'dark'
                                    ? theme.palette.error.main
                                    : '#F87171'
                              : 'primary.main',
                            '&:hover': {
                              backgroundColor: member.added
                                ? (theme) =>
                                    theme.palette.mode === 'dark'
                                      ? 'rgba(244, 67, 54, 0.2)'
                                      : '#FEE2E2'
                                : (theme) =>
                                    theme.palette.mode === 'dark'
                                      ? 'rgba(139, 92, 246, 0.2)'
                                      : '#EDE9FE',
                            },
                          }}
                        >
                          <Icon
                            icon={member.added ? 'eva:minus-fill' : 'eva:plus-fill'}
                            width={16}
                          />
                        </IconButton>
                      </Box>
                    ))}
                  </Box>
                </Card>
              </AccordionDetails>
            </Accordion>

            {/* Tools Accordion */}
            <Accordion
              expanded={toolsExpanded}
              onChange={() => setToolsExpanded(!toolsExpanded)}
              sx={{
                boxShadow: 'none',
                border: (theme) =>
                  theme.palette.mode === 'dark'
                    ? `1px solid ${theme.palette.grey[700]}`
                    : '1px solid #E5E7EB',
                borderRadius: '8px !important',
                bgcolor: (theme) =>
                  theme.palette.mode === 'dark'
                    ? theme.palette.grey[900]
                    : theme.palette.background.paper,
                '&:before': {
                  display: 'none',
                },
              }}
            >
              <AccordionSummary
                expandIcon={<Icon icon="eva:chevron-down-fill" />}
                sx={{
                  backgroundColor: (theme) =>
                    theme.palette.mode === 'dark'
                      ? theme.palette.grey[800]
                      : '#F9FAFB',
                  borderRadius: '8px',
                  '& .MuiAccordionSummary-content': {
                    margin: '12px 0',
                  },
                }}
              >
                <Typography variant="h6" fontWeight={600} color="text.primary">
                  Tools
                </Typography>
              </AccordionSummary>
              <AccordionDetails sx={{ p: 3 }}>
                <Card
                  sx={{
                    background: (theme) =>
                      theme.palette.mode === 'dark'
                        ? theme.palette.grey[800]
                        : theme.palette.background.neutral,
                    padding: '20px',
                    display: 'flex',
                    flexDirection: 'column',
                    gap: '16px',
                    border: (theme) =>
                      theme.palette.mode === 'dark'
                        ? `1px solid ${theme.palette.grey[700]}`
                        : 'none',
                  }}
                >
                  {/* Search Field */}
                  <TextField
                    fullWidth
                    placeholder="Search"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <Icon icon="eva:search-fill" />
                        </InputAdornment>
                      ),
                    }}
                    sx={{
                      mb: 3,
                      '& .MuiOutlinedInput-root': {
                        bgcolor: (theme) =>
                          theme.palette.mode === 'dark'
                            ? theme.palette.grey[700]
                            : '#F9FAFB',
                        borderRadius: '8px',
                        color: 'text.primary',
                        '& fieldset': {
                          borderColor: (theme) =>
                            theme.palette.mode === 'dark'
                              ? theme.palette.grey[600]
                              : '#E5E7EB',
                        },
                        '&:hover fieldset': {
                          borderColor: (theme) =>
                            theme.palette.mode === 'dark'
                              ? theme.palette.grey[500]
                              : '#D1D5DB',
                        },
                        '&.Mui-focused fieldset': {
                          borderColor: 'primary.main',
                        },
                      },
                      '& .MuiInputBase-input': {
                        color: 'text.primary',
                        '&::placeholder': {
                          color: 'text.secondary',
                          opacity: 1,
                        },
                      },
                    }}
                  />
                  <Stack spacing={2}>
                    {['Gmail', 'Facebook', 'LinkedIn'].map((tool) => (
                      <Card
                        key={tool}
                        variant="outlined"
                        sx={{
                          background: (theme) =>
                            theme.palette.mode === 'dark'
                              ? theme.palette.grey[700]
                              : '#F1EFF3',
                          p: 2,
                          borderColor: (theme) =>
                            theme.palette.mode === 'dark'
                              ? theme.palette.grey[600]
                              : theme.palette.divider,
                        }}
                      >
                        <Stack direction="row" alignItems="center" spacing={1}>
                          <Checkbox
                            sx={{
                              color: (theme) =>
                                theme.palette.mode === 'dark'
                                  ? theme.palette.grey[400]
                                  : 'inherit',
                              '&.Mui-checked': {
                                color: 'primary.main',
                              },
                            }}
                          />
                          <Iconify
                            icon={
                              tool === 'Gmail'
                                ? 'logos:google-gmail'
                                : tool === 'Facebook'
                                  ? 'logos:facebook'
                                  : 'logos:linkedin-icon'
                            }
                            width={24}
                            height={24}
                          />
                          <Typography variant="subtitle2" color="text.primary">{tool}</Typography>
                        </Stack>
                      </Card>
                    ))}
                  </Stack>
                </Card>
              </AccordionDetails>
            </Accordion>
          </Box>
        );
      case 2:
        return (
          <Box>
            <Typography variant="h5" fontWeight={700} mb={1} color="text.primary">
              Configuration
            </Typography>
            <Typography variant="body2" color="text.secondary" mb={1} fontSize="0.875rem">
              The tools that your team can require some info.
            </Typography>

            {/* Configuration Tools */}
            <Card
              sx={{
                p: '20px',
                background: (theme) =>
                  theme.palette.mode === 'dark'
                    ? theme.palette.grey[900]
                    : theme.palette.background.paper,
                border: (theme) =>
                  theme.palette.mode === 'dark'
                    ? `1px solid ${theme.palette.grey[700]}`
                    : 'none',
              }}
            >
              {configTools.map((tool) => (
                <Accordion
                  key={tool.id}
                  expanded={configExpanded[tool.id as keyof typeof configExpanded]}
                  onChange={() => handleConfigToggle(tool.id)}
                  sx={{
                    mb: 2,
                    boxShadow: 'none',
                    border: (theme) =>
                      theme.palette.mode === 'dark'
                        ? `1px solid ${theme.palette.grey[700]}`
                        : '1px solid #E5E7EB',
                    borderRadius: '8px !important',
                    bgcolor: (theme) =>
                      theme.palette.mode === 'dark'
                        ? theme.palette.grey[800]
                        : theme.palette.background.paper,
                    '&:before': {
                      display: 'none',
                    },
                  }}
                >
                  <AccordionSummary
                    expandIcon={<Icon icon="eva:chevron-down-fill" />}
                    sx={{
                      background: (theme) =>
                        theme.palette.mode === 'dark'
                          ? theme.palette.grey[700]
                          : theme.palette.background.neutral,
                      borderRadius: '8px',
                      '& .MuiAccordionSummary-content': {
                        margin: '12px 0',
                        alignItems: 'center',
                      },
                    }}
                  >
                    <Box display="flex" alignItems="center" gap={2} flex={1}>
                      <Icon icon={tool.icon} width={24} height={24} />
                      <Typography variant="body2" fontWeight={500} color="text.primary">
                        {tool.name}
                      </Typography>
                      {tool.status && (
                        <Chip
                          label={getStatusText(tool.status)}
                          size="small"
                          sx={{
                            backgroundColor: tool.status === 'linked'
                              ? (theme) =>
                                  theme.palette.mode === 'dark'
                                    ? 'rgba(76, 175, 80, 0.2)'
                                    : '#DCFCE7'
                              : (theme) =>
                                  theme.palette.mode === 'dark'
                                    ? 'rgba(244, 67, 54, 0.2)'
                                    : '#FEE2E2',
                            color: getStatusColor(tool.status),
                            fontWeight: 600,
                            fontSize: '0.75rem',
                            height: '24px',
                            border: (theme) =>
                              theme.palette.mode === 'dark'
                                ? `1px solid ${tool.status === 'linked' ? 'rgba(76, 175, 80, 0.3)' : 'rgba(244, 67, 54, 0.3)'}`
                                : 'none',
                            '& .MuiChip-label': {
                              px: 1,
                            },
                          }}
                        />
                      )}
                    </Box>
                  </AccordionSummary>
                  <AccordionDetails
                    sx={{
                      p: 3,
                      pt: 0,
                      background: (theme) =>
                        theme.palette.mode === 'dark'
                          ? theme.palette.grey[700]
                          : theme.palette.background.neutral
                    }}
                  >
                    <Box>
                      <Box display="flex" gap={2}>
                        <Field.Text fullWidth placeholder="API Key" name="apiKey" type="password" />
                        <AppButton
                          size="small"
                          variant="contained"
                          onClick={() => handleSaveApiKey(tool.id)}
                          sx={{
                            bgcolor: '#8B5CF6',
                            color: '#FFFFFF',
                            fontSize: '0.875rem',
                            fontWeight: 600,
                            textTransform: 'none',
                            borderRadius: '8px',
                            height: '32px',
                            width: '64px',
                            boxShadow: 'none',
                            '&:hover': {
                              bgcolor: '#7C3AED',
                              boxShadow: 'none',
                            },
                          }}
                          label="Save"
                        />
                      </Box>
                    </Box>
                  </AccordionDetails>
                </Accordion>
              ))}
            </Card>
          </Box>
        );
      case 3:
        return (
          <Box>
            <Typography variant="h5" fontWeight={700} mb={1} color="text.primary">
              Instructions
            </Typography>
            <Typography variant="body2" color="text.secondary" mb={1} fontSize="0.875rem">
              Please provide your team with your instructions
            </Typography>
            <Card
              sx={{
                padding: '20px',
                background: (theme) =>
                  theme.palette.mode === 'dark'
                    ? theme.palette.grey[900]
                    : theme.palette.background.paper,
                border: (theme) =>
                  theme.palette.mode === 'dark'
                    ? `1px solid ${theme.palette.grey[700]}`
                    : 'none',
              }}
            >
              <Field.Instruction name="instructions" label="" rows={6} />
            </Card>
          </Box>
        );
      case 4:
        return (
          <Box>
            <Typography variant="h5" fontWeight={700} mb={1} color="text.primary">
              Frequency
            </Typography>
            <Typography variant="body2" color="text.secondary" mb={1} fontSize="0.875rem">
              Please select the frequency at which the team should operate
            </Typography>
            <Card
              sx={{
                padding: '20px',
                background: (theme) =>
                  theme.palette.mode === 'dark'
                    ? theme.palette.grey[900]
                    : theme.palette.background.paper,
                border: (theme) =>
                  theme.palette.mode === 'dark'
                    ? `1px solid ${theme.palette.grey[700]}`
                    : 'none',
              }}
            >
              <Grid container sx={{ mt: '14px' }} spacing={2}>
                <Grid item xs={12} md={6}>
                  <Field.Select name="frequency" label="frequency" options={FREQUENCY_OPTIONS} />
                </Grid>
                <Grid item xs={12} md={6}>
                  <Field.DatePicker name="startDate" label="start date" />
                </Grid>
              </Grid>
            </Card>
          </Box>
        );
      default:
        return (
          <Box textAlign="center" py={4}>
            <Typography variant="h6" color="text.secondary">
              Step {step + 1} content will be implemented here
            </Typography>
          </Box>
        );
    }
  };

  return (
    <AppContainer title="Create Team" routeLinks={[{ name: 'Teams' }, { name: 'Create Team' }]}>
      {/* Stepper */}
      <Box sx={{ border: (theme) => `0.5px solid  ${theme.palette.divider}`, mt: '12px' }} />
      <Stepper
        activeStep={activeStep}
        alternativeLabel
        connector={null}
        sx={{
          mt: '12px',
          mb: 6,
          '& .MuiStepLabel-root': {
            padding: 0,
          },
          '& .MuiStepLabel-labelContainer': {
            marginTop: 2,
          },
          '& .MuiStepConnector-root': {
            display: 'none',
          },
        }}
      >
        {steps.map((label, index) => (
          <Step key={label}>
            <StepLabel
              StepIconComponent={({ active, completed }) => (
                <StepChipContainer active={active} completed={completed}>
                  <CustomStepIcon active={active} completed={completed}>
                    {completed ? <Icon icon="eva:checkmark-fill" width={20} /> : index + 1}
                  </CustomStepIcon>
                  <Typography
                    variant="body2"
                    sx={{
                      fontWeight: active ? 600 : 500,
                      color: active
                        ? 'text.primary'
                        : completed
                          ? 'success.main'
                          : 'text.secondary',
                      fontSize: '0.875rem',
                      marginTop: '8px',
                      textAlign: 'center',
                    }}
                  >
                    {label}
                  </Typography>
                </StepChipContainer>
              )}
              sx={{
                '& .MuiStepLabel-label': {
                  display: 'none', // Hide default label since we're using custom one
                },
              }}
            >
              {label}
            </StepLabel>
          </Step>
        ))}
      </Stepper>

      {/* Step Content */}
      <Box
        sx={{
          background: (theme) => theme.palette.background.neutral,
          padding: '20px',
          borderRadius: '16px',
        }}
      >
        <Form
          methods={methods}
          onSubmit={(_event?: React.FormEvent<HTMLFormElement>) => {
            // Always prevent default form submission
            // We'll handle submission manually with the Create Team button
            if (_event) _event.preventDefault();
          }}
        >
          <Box sx={{ mb: '16px' }}>{renderStepContent(activeStep)}</Box>
        </Form>

        {/* Action Buttons */}
        <Box display="flex" gap={3}>
          <AppButton
            size="small"
            variant="outlined"
            onClick={handleCancel}
            sx={{
              flex: 1,
              py: 2,
              borderColor: (theme: any) =>
                theme.palette.mode === 'dark'
                  ? theme.palette.grey[600]
                  : '#D1D5DB',
              color: 'primary.main',
              fontSize: '0.875rem',
              fontWeight: 600,
              textTransform: 'none',
              borderRadius: '8px',
              '&:hover': {
                borderColor: 'primary.main',
                bgcolor: (theme: any) =>
                  theme.palette.mode === 'dark'
                    ? 'rgba(139, 92, 246, 0.1)'
                    : 'rgba(139, 92, 246, 0.04)',
              },
            }}
            label="Cancel"
          />

          <AppButton
            size="small"
            variant="contained"
            onClick={activeStep === steps.length - 1 ? handleCancel : handleNext}
            sx={{
              flex: 1,
              py: 2,
              bgcolor: 'primary.main',
              fontSize: '0.875rem',
              fontWeight: 600,
              textTransform: 'none',
              borderRadius: '8px',
              boxShadow: 'none',
              '&:hover': {
                bgcolor: 'primary.dark',
                boxShadow: 'none',
              },
            }}
            label={activeStep === steps.length - 1 ? 'Create Team' : 'Next'}
          />
        </Box>
      </Box>
    </AppContainer>
  );
};

export default TeamsTemplateForm;
