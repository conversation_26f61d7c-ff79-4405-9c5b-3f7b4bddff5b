import { m } from 'framer-motion';

import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Container from '@mui/material/Container';
import Typography from '@mui/material/Typography';
import { useTranslation } from 'react-i18next';

import { RouterLink } from 'src/routes/components';

import { SimpleLayout } from 'src/layouts/simple';
import { PageNotFoundIllustration } from 'src/assets/illustrations';     

import { varBounce, MotionContainer } from 'src/components/animate';     

// ----------------------------------------------------------------------

export function NotFoundView() {
  const { t } = useTranslation();

  return (
    <SimpleLayout>
      <Container
        sx={{ display: 'flex', flexDirection: 'column', gap: 4, mt: '100px' }}
        component={MotionContainer}
      >
        <m.div variants={varBounce().in}>
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
            }}
          >
            <PageNotFoundIllustration />
          </Box>
        </m.div>
        <m.div variants={varBounce().in}>
          <Typography variant="h1" sx={{ textAlign: 'center' }}>
            {t('pages.error.notFound.title')}
          </Typography>
        </m.div>

        <m.div variants={varBounce().in}>
          <Typography variant="body1" sx={{ color: 'text.secondary', textAlign: 'center' }}>       
            {t('pages.error.notFound.description')}
          </Typography>
        </m.div>
        <Box sx={{ display: 'flex', justifyContent: 'center' }}>
          <Button
            sx={{ mt: '10px', ml: 'auto', mr: 'auto' }}
            component={RouterLink}
            href="/"
            size="large"
            variant="contained"
            color="primary"
          >
            {t('pages.error.notFound.goToHome')}
          </Button>
        </Box>
      </Container>
    </SimpleLayout>
  );
}
