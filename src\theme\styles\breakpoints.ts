// Custom breakpoints for better tablet support
export const customBreakpoints = {
  values: {
    xs: 0,
    sm: 600,
    md: 900,
    tablet: 768, // Custom tablet breakpoint
    lg: 1200,
    xl: 1536,
  },
};

// Media queries for custom breakpoints
export const customMediaQueries = {
  upXs: '@media (min-width:0px)',
  upSm: '@media (min-width:600px)',
  upTablet: '@media (min-width:768px)', // Custom tablet media query
  upMd: '@media (min-width:900px)',
  upLg: '@media (min-width:1200px)',
  upXl: '@media (min-width:1536px)',
  
  // Between queries for specific device ranges
  betweenSmTablet: '@media (min-width:600px) and (max-width:767px)',
  betweenTabletMd: '@media (min-width:768px) and (max-width:899px)',
  betweenTabletLg: '@media (min-width:768px) and (max-width:1199px)',
  
  // Down queries
  downTablet: '@media (max-width:767px)',
};
