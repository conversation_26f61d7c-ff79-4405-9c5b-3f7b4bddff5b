import { useState, useCallback } from 'react';

// Message types
export interface Message {
  id: string;
  content: string;
  role: 'user' | 'assistant';
  timestamp: Date;
  sender?: {
    name: string;
    avatar: string;
    role?: string;
  };
  isPrompt?: boolean;
  agentName?: string;
}

// Team member type
export interface TeamMember {
  id: string;
  name: string;
  avatar: string;
  role: string;
}

// Mock team members
const MOCK_TEAM_MEMBERS: TeamMember[] = [
  {
    id: 'member1',
    name: '<PERSON><PERSON><PERSON>',
    avatar: '/assets/images/avatars/avatar_1.jpg',
    role: 'UX team lead',
  },
  {
    id: 'member2',
    name: 'Data Analyzer',
    avatar: '/assets/images/avatars/avatar_2.jpg',
    role: 'Data Specialist',
  },
];

// Mock initial conversation
const MOCK_MESSAGES: Message[] = [
  {
    id: 'msg_1',
    content: 'Hello, introduce yourself please',
    role: 'user',
    timestamp: new Date(Date.now() - 1000 * 60 * 5),
    isPrompt: true,
  },
  {
    id: 'msg_2',
    content:
      "Hello, I'm <PERSON><PERSON><PERSON>, the UX team lead.\n\nI'll be happy to help you with any UX process as well as providing the best practices!\n\nFirst, @DataAnalyzer please provide the latest on www.example.com website",
    role: 'assistant',
    timestamp: new Date(Date.now() - 1000 * 60 * 4),
    sender: MOCK_TEAM_MEMBERS[0],
  },
  {
    id: 'msg_3',
    content:
      "Your request has been successfully processed!\n\nI'm going to provide the latest news, features and updates on the website you mentioned, so you can take the next step easily!",
    role: 'assistant',
    timestamp: new Date(Date.now() - 1000 * 60 * 3),
    sender: MOCK_TEAM_MEMBERS[1],
  },
];

// Empty initial messages
const EMPTY_MESSAGES: Message[] = [];

export function useTeamChat(teamName: string = 'AI Assistant') {
  // Start with empty messages for new chats
  const [messages, setMessages] = useState<Message[]>(EMPTY_MESSAGES);
  const [isTyping, setIsTyping] = useState(false);
  const [teamMembers] = useState<TeamMember[]>(MOCK_TEAM_MEMBERS);

  // Function to generate a unique ID
  const generateId = () => `msg_${Date.now()}_${Math.random().toString(36).slice(2, 11)}`;

  // Function to handle sending a message
  const handleSendMessage = useCallback(
    (content: string) => {
      if (!content.trim()) return;

      // Add user message
      const userMessage: Message = {
        id: generateId(),
        content,
        role: 'user',
        timestamp: new Date(),
        isPrompt: false, // Regular user message, not a prompt
      };

      setMessages((prev) => [...prev, userMessage]);
      setIsTyping(true);

      // Simulate AI response after a delay
      setTimeout(() => {
        // Determine which team member should respond
        const respondingMember = teamMembers[0]; // Default to first team member (Naofumi)

        // Generate AI response based on user message
        let aiResponse = '';

        if (content.toLowerCase().includes('hello') || content.toLowerCase().includes('hi')) {
          aiResponse = `Hello! I'm ${respondingMember.name}, the ${respondingMember.role}. How can I assist you today?`;
        } else if (content.toLowerCase().includes('help')) {
          aiResponse = `I'm ${respondingMember.name}, and I'm here to help! What do you need assistance with?`;
        } else if (content.toLowerCase().includes('thank')) {
          aiResponse = "You're welcome! Is there anything else I can help you with?";
        } else if (
          content.toLowerCase().includes('data') ||
          content.toLowerCase().includes('analyze')
        ) {
          // Switch to Data Analyzer for data-related queries
          const dataAnalyzer = teamMembers[1];
          aiResponse = `I'm ${dataAnalyzer.name}. I'll analyze the data you've provided and get back to you with insights shortly.`;

          // Add AI response from Data Analyzer
          const assistantMessage: Message = {
            id: generateId(),
            content: aiResponse,
            role: 'assistant',
            timestamp: new Date(),
            sender: dataAnalyzer,
          };

          setMessages((prev) => [...prev, assistantMessage]);
          setIsTyping(false);
          return;
        } else {
          aiResponse = `I understand your request. As the ${respondingMember.role}, I'll help you with this. Let me know if you need any specific UX guidance.`;
        }

        // Add AI response from the responding team member
        const assistantMessage: Message = {
          id: generateId(),
          content: aiResponse,
          role: 'assistant',
          timestamp: new Date(),
          sender: respondingMember,
        };

        setMessages((prev) => [...prev, assistantMessage]);
        setIsTyping(false);
      }, 1500); // Simulate typing delay
    },
    [teamName, teamMembers]
  );

  // Function to clear messages (for new chats)
  const clearMessages = useCallback(() => {
    setMessages(EMPTY_MESSAGES);
  }, []);

  return {
    messages,
    isTyping,
    handleSendMessage,
    teamMembers,
    clearMessages,
  };
}
