import { useState, useEffect } from 'react';
import { useTeamsApi, TeamTemplate } from 'src/services/api/use-teams-api';

// Filter options
const TYPE_FILTERS = ['All', 'AUTO', 'MANUAL'];
const CATEGORY_FILTERS = ['All', 'marketing', 'sales', 'social media'];

export const useTeamsView = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredTeams, setFilteredTeams] = useState<TeamTemplate[]>([]);
  const [selectedTypeTab, setSelectedTypeTab] = useState(0);
  const [selectedCategoryTab, setSelectedCategoryTab] = useState(0);

  // Use the teams API hook to fetch data
  const { useGetTeamTemplates } = useTeamsApi();
  const {
    data: teamTemplatesResponse,
    isLoading,
    error,
    refetch,
  } = useGetTeamTemplates();

  // Extract team templates from the response
  const teamTemplates = teamTemplatesResponse?.templatesTeams || [];

  // Update filtered teams when templates data changes
  useEffect(() => {
    filterTeams(searchQuery, selectedTypeTab, selectedCategoryTab);
  }, [teamTemplates, searchQuery, selectedTypeTab, selectedCategoryTab]);

  // Filter teams based on search, type, and category
  const handleSearch = (query: string) => {
    setSearchQuery(query);
    filterTeams(query, selectedTypeTab, selectedCategoryTab);
  };

  const handleTypeTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setSelectedTypeTab(newValue);
    filterTeams(searchQuery, newValue, selectedCategoryTab);
  };

  const handleCategoryTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setSelectedCategoryTab(newValue);
    filterTeams(searchQuery, selectedTypeTab, newValue);
  };

  const filterTeams = (query: string, typeTabIndex: number, categoryTabIndex: number) => {
    let filtered = teamTemplates;

    // Filter by search query
    if (query) {
      filtered = filtered.filter(
        (team) =>
          team.description.toLowerCase().includes(query.toLowerCase()) ||
          team.category.name.toLowerCase().includes(query.toLowerCase())
      );
    }

    // Filter by type
    if (typeTabIndex !== 0) {
      // 0 = All
      const typeName = TYPE_FILTERS[typeTabIndex];
      filtered = filtered.filter((team) => team.type === typeName);
    }

    // Filter by category
    if (categoryTabIndex !== 0) {
      // 0 = All
      const categoryName = CATEGORY_FILTERS[categoryTabIndex];
      filtered = filtered.filter((team) =>
        team.category.name.toLowerCase() === categoryName.toLowerCase()
      );
    }

    setFilteredTeams(filtered);
  };

  // Get recently used teams (for now, just return first template if available)
  const recentlyUsedTeams = teamTemplates.slice(0, 1);

  return {
    // Data
    teamTemplates,
    filteredTeams,
    recentlyUsedTeams,

    // Loading states
    isLoading,
    error,
    refetch,

    // Search and filter state
    searchQuery,
    selectedTypeTab,
    selectedCategoryTab,

    // Filter options
    TYPE_FILTERS,
    CATEGORY_FILTERS,

    // Event handlers
    handleSearch,
    handleTypeTabChange,
    handleCategoryTabChange,
  };
};

export type { TeamTemplate };
