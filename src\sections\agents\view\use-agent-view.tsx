import { useState, useEffect } from 'react';
import { useTemplatesApi, Template } from 'src/services/api/use-templates-api';

// Filter options
const TYPE_FILTERS = ['All', 'SINGLE', 'TEAM'];
const CATEGORY_FILTERS = ['All', 'sales', 'marketing', 'social media'];

export const useAgentView = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredAgents, setFilteredAgents] = useState<Template[]>([]);
  const [selectedTypeTab, setSelectedTypeTab] = useState(0);
  const [selectedCategoryTab, setSelectedCategoryTab] = useState(0);

  // Use the templates API hook to fetch data
  const { useGetTemplates } = useTemplatesApi();
  const {
    data: templatesResponse,
    isLoading,
    error,
    refetch,
  } = useGetTemplates();

  // Extract templates from the response
  const templates = templatesResponse?.templates || [];

  // Update filtered agents when templates data changes
  useEffect(() => {
    filterAgents(searchQuery, selectedTypeTab, selectedCategoryTab);
  }, [templates, searchQuery, selectedTypeTab, selectedCategoryTab]);

  // Filter agents based on search, type, and category
  const handleSearch = (query: string) => {
    setSearchQuery(query);
    filterAgents(query, selectedTypeTab, selectedCategoryTab);
  };

  const handleTypeTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setSelectedTypeTab(newValue);
    filterAgents(searchQuery, newValue, selectedCategoryTab);
  };

  const handleCategoryTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setSelectedCategoryTab(newValue);
    filterAgents(searchQuery, selectedTypeTab, newValue);
  };

  const filterAgents = (query: string, typeTabIndex: number, categoryTabIndex: number) => {
    let filtered = templates;

    // Filter by search query
    if (query) {
      filtered = filtered.filter(
        (agent) =>
          agent.name.toLowerCase().includes(query.toLowerCase()) ||
          agent.description.toLowerCase().includes(query.toLowerCase()) ||
          agent.category.name.toLowerCase().includes(query.toLowerCase())
      );
    }

    // Filter by type
    if (typeTabIndex !== 0) {
      // 0 = All
      const typeName = TYPE_FILTERS[typeTabIndex];
      filtered = filtered.filter((agent) => agent.type === typeName);
    }

    // Filter by category
    if (categoryTabIndex !== 0) {
      // 0 = All
      const categoryName = CATEGORY_FILTERS[categoryTabIndex];
      filtered = filtered.filter((agent) => agent.category.name.toLowerCase() === categoryName.toLowerCase());
    }

    setFilteredAgents(filtered);
  };

  return {
    // Data
    templates,
    filteredAgents,

    // Loading states
    isLoading,
    error,
    refetch,

    // Search and filter state
    searchQuery,
    selectedTypeTab,
    selectedCategoryTab,

    // Filter options
    TYPE_FILTERS,
    CATEGORY_FILTERS,

    // Event handlers
    handleSearch,
    handleTypeTabChange,
    handleCategoryTabChange,
  };
};

export type { Template };
