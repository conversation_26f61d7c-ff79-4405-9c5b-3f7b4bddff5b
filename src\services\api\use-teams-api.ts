import axiosInstance from 'src/utils/axios';
import { useApiServices } from 'src/services/hooks/use-api-services';

// Define the API endpoints for team templates
export const teamTemplateEndpoints = {
  list: '/templates-teams',
  details: '/templates-teams',
};

// Define the Category interface
export interface TeamCategory {
  name: string;
  description: string;
  icon: string;
  theme: string;
}

// Define the TeamTemplate data type based on the new API response
export interface TeamTemplate {
  id: number;
  description: string;
  type: 'AUTO' | 'MANUAL';
  model: 'GEMINI_1_5_FLASH' | 'GPT_4O_MINI' | 'GPT_4O' | 'CLAUDE_3_5_SONNET';
  category: TeamCategory;
}

// Define the API response structure
export interface TeamTemplatesResponse {
  templatesTeams: TeamTemplate[];
}

// Create a hook to use the team templates API
export const useTeamsApi = () => {
  const apiServices = useApiServices({ axiosInstance });

  // Get all team templates
  const useGetTeamTemplates = () => {
    return apiServices.useGetListService<TeamTemplatesResponse>({
      url: teamTemplateEndpoints.list,
    });
  };

  // Get a single team template by ID
  const useGetTeamTemplate = (id: string) => {
    return apiServices.useGetItemService<TeamTemplate>({
      url: teamTemplateEndpoints.details,
      id,
    });
  };

  // Create a new team template
  const useCreateTeamTemplate = (onSuccess?: (data: any) => void) => {
    return apiServices.usePostService<TeamTemplate, any>({
      url: teamTemplateEndpoints.list,
      onSuccess,
    });
  };

  // Update a team template
  const useUpdateTeamTemplate = (id: string, onSuccess?: () => void) => {
    return apiServices.usePutService<TeamTemplate>({
      url: teamTemplateEndpoints.details,
      id,
      onSuccess,
    });
  };

  // Delete a team template
  const useDeleteTeamTemplate = (onSuccess?: () => void) => {
    return apiServices.useDeleteService<any>({
      url: teamTemplateEndpoints.details,
      onSuccess,
    });
  };

  return {
    useGetTeamTemplates,
    useGetTeamTemplate,
    useCreateTeamTemplate,
    useUpdateTeamTemplate,
    useDeleteTeamTemplate,
  };
};
